package ee.tak24.kanban.model;

/**
 * Column model class representing a column in a KanBan board
 */
public class Column {
    private Integer id;
    private Integer boardId;
    private String name;
    private Integer position;
    private String color;

    public Column() {
        this.color = "#808080"; // Default gray color
    }

    public Column(Integer boardId, String name, Integer position) {
        this();
        this.boardId = boardId;
        this.name = name;
        this.position = position;
    }

    public Column(Integer id, Integer boardId, String name, Integer position, String color) {
        this.id = id;
        this.boardId = boardId;
        this.name = name;
        this.position = position;
        this.color = color;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBoardId() {
        return boardId;
    }

    public void setBoardId(Integer boardId) {
        this.boardId = boardId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    @Override
    public String toString() {
        return "Column{" +
                "id=" + id +
                ", boardId=" + boardId +
                ", name='" + name + '\'' +
                ", position=" + position +
                ", color='" + color + '\'' +
                '}';
    }
}
