package ee.tak24.kanban.database;

import ee.tak24.kanban.model.Board;
import ee.tak24.kanban.model.Column;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Data Access Object for Board operations
 */
public class BoardDAO {

    /**
     * Save a new board to database
     */
    public Board save(Board board) throws SQLException {
        String sql = "INSERT INTO boards (name, created_at) VALUES (?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, board.getName());
            stmt.setTimestamp(2, Timestamp.valueOf(board.getCreatedAt()));
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating board failed, no rows affected.");
            }

            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    board.setId(generatedKeys.getInt(1));
                    
                    // Create default columns for new board
                    createDefaultColumns(board.getId());
                    
                    return board;
                } else {
                    throw new SQLException("Creating board failed, no ID obtained.");
                }
            }
        }
    }

    /**
     * Create default columns (TODO, IN PROGRESS, DONE) for a new board
     */
    private void createDefaultColumns(Integer boardId) throws SQLException {
        String sql = "INSERT INTO columns (board_id, name, position, color) VALUES (?, ?, ?, ?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            // TODO column
            stmt.setInt(1, boardId);
            stmt.setString(2, "TODO");
            stmt.setInt(3, 1);
            stmt.setString(4, "#FF6B6B");
            stmt.executeUpdate();
            
            // IN PROGRESS column
            stmt.setInt(1, boardId);
            stmt.setString(2, "IN PROGRESS");
            stmt.setInt(3, 2);
            stmt.setString(4, "#4ECDC4");
            stmt.executeUpdate();
            
            // DONE column
            stmt.setInt(1, boardId);
            stmt.setString(2, "DONE");
            stmt.setInt(3, 3);
            stmt.setString(4, "#45B7D1");
            stmt.executeUpdate();
        }
    }

    /**
     * Find board by ID
     */
    public Board findById(Integer id) throws SQLException {
        String sql = "SELECT id, name, created_at FROM boards WHERE id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return new Board(
                        rs.getInt("id"),
                        rs.getString("name"),
                        rs.getTimestamp("created_at").toLocalDateTime()
                    );
                }
            }
        }
        return null;
    }

    /**
     * Find all boards
     */
    public List<Board> findAll() throws SQLException {
        List<Board> boards = new ArrayList<>();
        String sql = "SELECT id, name, created_at FROM boards ORDER BY created_at DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                boards.add(new Board(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getTimestamp("created_at").toLocalDateTime()
                ));
            }
        }
        return boards;
    }

    /**
     * Update board
     */
    public void update(Board board) throws SQLException {
        String sql = "UPDATE boards SET name = ? WHERE id = ?";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, board.getName());
            stmt.setInt(2, board.getId());
            stmt.executeUpdate();
        }
    }

    /**
     * Delete board and all related data
     */
    public void delete(Integer id) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection()) {
            conn.setAutoCommit(false);
            
            try {
                // Delete task_tags first
                try (PreparedStatement stmt = conn.prepareStatement(
                    "DELETE FROM task_tags WHERE task_id IN (SELECT t.id FROM tasks t JOIN columns c ON t.column_id = c.id WHERE c.board_id = ?)")) {
                    stmt.setInt(1, id);
                    stmt.executeUpdate();
                }
                
                // Delete tasks
                try (PreparedStatement stmt = conn.prepareStatement(
                    "DELETE FROM tasks WHERE column_id IN (SELECT id FROM columns WHERE board_id = ?)")) {
                    stmt.setInt(1, id);
                    stmt.executeUpdate();
                }
                
                // Delete columns
                try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM columns WHERE board_id = ?")) {
                    stmt.setInt(1, id);
                    stmt.executeUpdate();
                }
                
                // Delete board
                try (PreparedStatement stmt = conn.prepareStatement("DELETE FROM boards WHERE id = ?")) {
                    stmt.setInt(1, id);
                    stmt.executeUpdate();
                }
                
                conn.commit();
            } catch (SQLException e) {
                conn.rollback();
                throw e;
            } finally {
                conn.setAutoCommit(true);
            }
        }
    }
}
