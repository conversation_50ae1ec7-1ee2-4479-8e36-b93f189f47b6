@echo off
echo Compiling Java sources...

REM Create output directories
if not exist "out\classes" mkdir out\classes
if not exist "out\test-classes" mkdir out\test-classes

REM Download dependencies if not exist
if not exist "lib" mkdir lib

REM Download JavaFX if not exists
if not exist "lib\javafx-controls-19.jar" (
    echo Downloading JavaFX Controls...
    curl -L -o lib\javafx-controls-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-controls/19/javafx-controls-19.jar
)

if not exist "lib\javafx-base-19.jar" (
    echo Downloading JavaFX Base...
    curl -L -o lib\javafx-base-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-base/19/javafx-base-19.jar
)

if not exist "lib\javafx-graphics-19.jar" (
    echo Downloading JavaFX Graphics...
    curl -L -o lib\javafx-graphics-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-graphics/19/javafx-graphics-19.jar
)

if not exist "lib\javafx-fxml-19.jar" (
    echo Downloading JavaFX FXML...
    curl -L -o lib\javafx-fxml-19.jar https://repo1.maven.org/maven2/org/openjfx/javafx-fxml/19/javafx-fxml-19.jar
)

REM Download SQLite JDBC if not exists
if not exist "lib\sqlite-jdbc-3.42.0.0.jar" (
    echo Downloading SQLite JDBC...
    curl -L -o lib\sqlite-jdbc-3.42.0.0.jar https://repo1.maven.org/maven2/org/xerial/sqlite-jdbc/3.42.0.0/sqlite-jdbc-3.42.0.0.jar
)

REM Download JUnit 5 if not exists
if not exist "lib\junit-jupiter-5.9.3.jar" (
    echo Downloading JUnit Jupiter...
    curl -L -o lib\junit-jupiter-5.9.3.jar https://repo1.maven.org/maven2/org/junit/jupiter/junit-jupiter/5.9.3/junit-jupiter-5.9.3.jar
)

if not exist "lib\junit-jupiter-api-5.9.3.jar" (
    echo Downloading JUnit Jupiter API...
    curl -L -o lib\junit-jupiter-api-5.9.3.jar https://repo1.maven.org/maven2/org/junit/jupiter/junit-jupiter-api/5.9.3/junit-jupiter-api-5.9.3.jar
)

if not exist "lib\junit-jupiter-engine-5.9.3.jar" (
    echo Downloading JUnit Jupiter Engine...
    curl -L -o lib\junit-jupiter-engine-5.9.3.jar https://repo1.maven.org/maven2/org/junit/jupiter/junit-jupiter-engine/5.9.3/junit-jupiter-engine-5.9.3.jar
)

if not exist "lib\junit-platform-commons-1.9.3.jar" (
    echo Downloading JUnit Platform Commons...
    curl -L -o lib\junit-platform-commons-1.9.3.jar https://repo1.maven.org/maven2/org/junit/platform/junit-platform-commons/1.9.3/junit-platform-commons-1.9.3.jar
)

if not exist "lib\junit-platform-engine-1.9.3.jar" (
    echo Downloading JUnit Platform Engine...
    curl -L -o lib\junit-platform-engine-1.9.3.jar https://repo1.maven.org/maven2/org/junit/platform/junit-platform-engine/1.9.3/junit-platform-engine-1.9.3.jar
)

REM Compile main sources
echo Compiling main sources...
javac -cp "lib\*" -d out\classes src\main\java\ee\tak24\kanban\*.java src\main\java\ee\tak24\kanban\model\*.java src\main\java\ee\tak24\kanban\database\*.java src\main\java\ee\tak24\kanban\service\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    exit /b 1
)

REM Compile test sources
echo Compiling test sources...
javac -cp "out\classes;lib\*" -d out\test-classes src\test\java\ee\tak24\kanban\service\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Test compilation failed!
    exit /b 1
)

echo Compilation successful!

REM Run tests
echo Running tests...
java -cp "out\classes;out\test-classes;lib\*" org.junit.platform.console.ConsoleLauncher --class-path="out\classes;out\test-classes;lib\*" --scan-class-path

echo Done!
