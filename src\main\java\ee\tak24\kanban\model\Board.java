package ee.tak24.kanban.model;

import java.time.LocalDateTime;

/**
 * Board model class representing a KanBan board
 */
public class Board {
    private Integer id;
    private String name;
    private LocalDateTime createdAt;

    public Board() {
        this.createdAt = LocalDateTime.now();
    }

    public Board(String name) {
        this();
        this.name = name;
    }

    public Board(Integer id, String name, LocalDateTime createdAt) {
        this.id = id;
        this.name = name;
        this.createdAt = createdAt;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "Board{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
