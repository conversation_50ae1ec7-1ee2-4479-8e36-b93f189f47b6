package ee.tak24.kanban;

/**
 * Main Application class for KanBan Board
 * JavaFX UI will be added later
 */
public class KanbanApplication {

    public static void main(String[] args) {
        System.out.println("KanBan Board - TAK24");
        System.out.println("Application started successfully!");

        // For now, just test database connection
        try {
            ee.tak24.kanban.database.DatabaseConnection.getConnection();
            System.out.println("Database connection successful!");
        } catch (Exception e) {
            System.err.println("Database connection failed: " + e.getMessage());
        }
    }
}
