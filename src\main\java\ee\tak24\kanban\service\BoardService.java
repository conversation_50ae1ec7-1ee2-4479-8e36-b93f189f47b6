package ee.tak24.kanban.service;

import ee.tak24.kanban.database.BoardDAO;
import ee.tak24.kanban.model.Board;

import java.sql.SQLException;
import java.util.List;

/**
 * Service class for Board business logic
 */
public class BoardService {
    private final BoardDAO boardDAO;

    public BoardService() {
        this.boardDAO = new BoardDAO();
    }

    public BoardService(BoardDAO boardDAO) {
        this.boardDAO = boardDAO;
    }

    /**
     * Create a new board with validation
     */
    public Board createBoard(String name) throws SQLException {
        validateBoardName(name);
        
        Board board = new Board(name);
        return boardDAO.save(board);
    }

    /**
     * Get board by ID
     */
    public Board getBoardById(Integer id) throws SQLException {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("Board ID must be a positive number");
        }
        
        Board board = boardDAO.findById(id);
        if (board == null) {
            throw new IllegalArgumentException("Board not found with ID: " + id);
        }
        
        return board;
    }

    /**
     * Get all boards
     */
    public List<Board> getAllBoards() throws SQLException {
        return boardDAO.findAll();
    }

    /**
     * Update board name
     */
    public void updateBoardName(Integer id, String newName) throws SQLException {
        validateBoardName(newName);
        
        Board board = getBoardById(id); // This will throw exception if not found
        board.setName(newName);
        boardDAO.update(board);
    }

    /**
     * Delete board
     */
    public void deleteBoard(Integer id) throws SQLException {
        getBoardById(id); // Validate board exists
        boardDAO.delete(id);
    }

    /**
     * Validate board name
     */
    private void validateBoardName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Board name cannot be empty");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("Board name cannot be longer than 100 characters");
        }
    }
}
