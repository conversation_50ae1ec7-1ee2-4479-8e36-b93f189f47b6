# KanBan Board - Töölauarakenduse Projekt
**Grupp: TAK24**

## 📋 Projekti Ülevaade

### Eesmärk
Luua JavaFX põhine KanBan board töölauarakendus SQLite andmebaasiga, mis võimaldab kasutajatel hallata ülesandeid visuaalsel tahvlil.

### Tehnoloogia Stack
- **Programmeerimiskeel:** Java 17+
- **UI Framework:** JavaFX
- **Andmebaas:** SQLite (embedded)
- **Build Tool:** Maven/Gradle
- **Testimine:** JUnit 5
- **Versioonihaldus:** Git/GitHub

## 🏗️ Arhitektuur

### MVC Pattern
```
src/
├── main/
│   ├── java/
│   │   ├── controller/   # FXML kontrollerid
│   │   ├── model/        # Domeeni klassid ja DAO
│   │   ├── view/         # FXML failid
│   │   ├── service/      # Äriloogika (DRY põhimõte)
│   │   ├── database/     # DB ühendus ja migratsioonid
│   │   └── utils/        # Abiklassid
│   └── resources/
│       ├── fxml/         # UI kirjeldused
│       ├── css/          # Stiilid
│       └── db/           # SQL skriptid
└── test/
    └── java/             # Automaattestid
```

### DRY Põhimõtted
1. **Service Layer:** Kogu äriloogika eraldi service klassides
2. **DAO Pattern:** Andmebaasi operatsioonid eraldi klassides
3. **Utility Classes:** Korduvkasutatavad komponendid (nt DragDropHandler)
4. **Base Classes:** Abstraktsed klassid ühise funktsionaalsuse jaoks

## 📊 Andmebaasi Mudel

```sql
-- Tahvlid
CREATE TABLE boards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Veerud
CREATE TABLE columns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    board_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    position INTEGER NOT NULL,
    color TEXT DEFAULT '#808080',
    FOREIGN KEY (board_id) REFERENCES boards(id)
);

-- Ülesanded
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    column_id INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    priority TEXT CHECK(priority IN ('LOW', 'MEDIUM', 'HIGH')),
    position INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date DATE,
    FOREIGN KEY (column_id) REFERENCES columns(id)
);

-- Sildid
CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    color TEXT NOT NULL
);

-- Ülesannete-siltide seos
CREATE TABLE task_tags (
    task_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    PRIMARY KEY (task_id, tag_id),
    FOREIGN KEY (task_id) REFERENCES tasks(id),
    FOREIGN KEY (tag_id) REFERENCES tags(id)
);
```

## 📝 Kasutajalood

### 1. Tahvli loomine
**Kasutajana soovin luua uue KanBan tahvli, et alustada projektiga.**
- **Aktsepteerimistingimused:**
  - ✓ Kasutaja saab sisestada tahvli nime
  - ✓ Tahvel luuakse vaikimisi 3 veeruga (TODO, IN PROGRESS, DONE)
  - ✓ Tahvel salvestatakse andmebaasi
  - ✓ Uus tahvel avaneb kohe peale loomist
- **Ajahinnang:** 30-60 min

### 2. Ülesande lisamine
**Kasutajana soovin lisada ülesande veergu, et planeerida tööd.**
- **Aktsepteerimistingimused:**
  - ✓ Kasutaja saab sisestada ülesande pealkirja
  - ✓ Kasutaja saab lisada vabatahtliku kirjelduse
  - ✓ Ülesanne ilmub valitud veergu
  - ✓ Ülesanne salvestatakse andmebaasi
- **Ajahinnang:** 20-40 min

### 3. Ülesande liigutamine
**Kasutajana soovin lohistada ülesandeid veergude vahel, et näidata progressi.**
- **Aktsepteerimistingimused:**
  - ✓ Ülesannet saab hiirega lohistada
  - ✓ Ülesanne liigub teise veergu kukutamisel
  - ✓ Muudatus salvestatakse andmebaasi
  - ✓ Ülesannete järjekord säilib
- **Ajahinnang:** 45-90 min

### 4. Veeru lisamine
**Kasutajana soovin lisada tahvlile uue veeru, et kohandada töövoogu.**
- **Aktsepteerimistingimused:**
  - ✓ Kasutaja saab sisestada veeru nime
  - ✓ Uus veerg ilmub tahvlile
  - ✓ Veerg salvestatakse andmebaasi
  - ✓ Veergu saab positsioneerida teiste suhtes
- **Ajahinnang:** 25-45 min

### 5. Ülesande muutmine
**Kasutajana soovin muuta ülesande andmeid, et hoida info ajakohasena.**
- **Aktsepteerimistingimused:**
  - ✓ Topeltklõps ülesandel avab muutmise dialoogi
  - ✓ Saab muuta pealkirja ja kirjeldust
  - ✓ Muudatused salvestatakse andmebaasi
  - ✓ UI uueneb koheselt
- **Ajahinnang:** 20-35 min

### 6. Ülesande kustutamine
**Kasutajana soovin kustutada ülesande, mis pole enam vajalik.**
- **Aktsepteerimistingimused:**
  - ✓ Ülesandel on kustutamise nupp
  - ✓ Kuvatakse kinnituse dialoog
  - ✓ Ülesanne eemaldatakse veerust
  - ✓ Kustutamine salvestatakse andmebaasi
- **Ajahinnang:** 15-25 min

### 7. Prioriteedi määramine
**Kasutajana soovin määrata ülesandele prioriteedi, et eristada olulisi ülesandeid.**
- **Aktsepteerimistingimused:**
  - ✓ Saab valida LOW, MEDIUM või HIGH prioriteedi
  - ✓ Prioriteet kuvatakse visuaalselt (värv/ikoon)
  - ✓ Prioriteet salvestatakse andmebaasi
  - ✓ Ülesandeid saab sorteerida prioriteedi järgi
- **Ajahinnang:** 25-40 min

### 8. Tahvlite nimekiri
**Kasutajana soovin näha kõiki oma tahvleid, et valida õige.**
- **Aktsepteerimistingimused:**
  - ✓ Kuvatakse kõik olemasolevad tahvlid
  - ✓ Iga tahvli juures on nimi ja loomise kuupäev
  - ✓ Tahvlile klõpsates see avaneb
  - ✓ Nimekiri uueneb uue tahvli loomisel
- **Ajahinnang:** 20-35 min

### 9. Veeru kustutamine
**Kasutajana soovin kustutada tühja veeru, et lihtsustada tahvlit.**
- **Aktsepteerimistingimused:**
  - ✓ Tühja veeru saab kustutada
  - ✓ Veergu ülesannetega ei saa kustutada
  - ✓ Kuvatakse hoiatus, kui veerus on ülesandeid
  - ✓ Kustutamine salvestatakse andmebaasi
- **Ajahinnang:** 20-30 min

### 10. Siltide lisamine
**Kasutajana soovin lisada ülesandele silte, et kategoriseerida neid.**
- **Aktsepteerimistingimused:**
  - ✓ Saab luua uue sildi nime ja värviga
  - ✓ Saab valida olemasolevaid silte
  - ✓ Sildid kuvatakse ülesande juures
  - ✓ Sildid salvestatakse andmebaasi
- **Ajahinnang:** 30-50 min

### 11. Tähtaja määramine
**Kasutajana soovin määrata ülesandele tähtaja, et jälgida ajakava.**
- **Aktsepteerimistingimused:**
  - ✓ Saab valida kuupäeva kalendrist
  - ✓ Tähtaeg kuvatakse ülesandel
  - ✓ Üle tähtaja ülesanded on visuaalselt eristatavad
  - ✓ Tähtaeg salvestatakse andmebaasi
- **Ajahinnang:** 25-40 min

### 12. Otsing
**Kasutajana soovin otsida ülesandeid pealkirja järgi, et kiiresti leida vajalik.**
- **Aktsepteerimistingimused:**
  - ✓ Otsingukast on nähtav
  - ✓ Otsing toimub reaalajas tippimise ajal
  - ✓ Mittevasted peidetakse
  - ✓ Otsingu tühjendamisel kuvatakse kõik
- **Ajahinnang:** 20-35 min

### 13. Filtreerimine prioriteedi järgi
**Kasutajana soovin filtreerida ülesandeid prioriteedi järgi, et keskenduda olulistele.**
- **Aktsepteerimistingimused:**
  - ✓ Saab valida prioriteedi filtri
  - ✓ Kuvatakse ainult valitud prioriteediga ülesanded
  - ✓ Saab kombineerida mitut prioriteeti
  - ✓ Filter on eemaldatav
- **Ajahinnang:** 25-40 min

### 14. Tahvli kustutamine
**Kasutajana soovin kustutada terve tahvli, kui projekt on lõppenud.**
- **Aktsepteerimistingimused:**
  - ✓ Tahvli saab kustutada nimekirjast
  - ✓ Küsitakse kinnitust enne kustutamist
  - ✓ Kõik seotud andmed kustutatakse
  - ✓ Suunatakse tagasi tahvlite nimekirja
- **Ajahinnang:** 20-30 min

### 15. Veeru värvi muutmine
**Kasutajana soovin muuta veeru värvi, et visuaalselt eristada etappe.**
- **Aktsepteerimistingimused:**
  - ✓ Saab valida eeldefineeritud värvidest
  - ✓ Värv rakendub veeru päisele
  - ✓ Värv salvestatakse andmebaasi
  - ✓ Värv säilib sessiooni vahel
- **Ajahinnang:** 15-25 min

### 16. Ülesannete järjestamine veerus
**Kasutajana soovin muuta ülesannete järjekorda veerus, et prioritiseerida.**
- **Aktsepteerimistingimused:**
  - ✓ Ülesandeid saab lohistada üles-alla
  - ✓ Uus järjekord säilib
  - ✓ Järjekord salvestatakse andmebaasi
  - ✓ Visuaalne tagasiside lohistamisel
- **Ajahinnang:** 30-45 min

### 17. Tahvli nime muutmine
**Kasutajana soovin muuta tahvli nime, et see peegeldaks projekti muutust.**
- **Aktsepteerimistingimused:**
  - ✓ Tahvli nimel klõpsates saab seda muuta
  - ✓ Uus nimi valideeritakse (mitte tühi)
  - ✓ Muudatus salvestatakse andmebaasi
  - ✓ Uus nimi kuvatakse koheselt
- **Ajahinnang:** 15-20 min

### 18. Statistika vaatamine
**Kasutajana soovin näha tahvli statistikat, et jälgida progressi.**
- **Aktsepteerimistingimused:**
  - ✓ Kuvatakse ülesannete arv veeru kohta
  - ✓ Kuvatakse ülesannete koguarv
  - ✓ Kuvatakse tähtaegade ülevaade
  - ✓ Statistika uueneb automaatselt
- **Ajahinnang:** 25-40 min

### 19. Automaatne salvestamine
**Kasutajana soovin, et muudatused salvestuksid automaatselt.**
- **Aktsepteerimistingimused:**
  - ✓ Iga muudatus salvestatakse koheselt
  - ✓ Salvestamise viga kuvatakse kasutajale
  - ✓ Võrguühenduse kadumisel hoiatus
  - ✓ Ei vaja eraldi salvestamise nuppu
- **Ajahinnang:** 30-45 min

### 20. Klaviatuuri otseteed
**Kasutajana soovin kasutada klaviatuuri otseteid, et töötada kiiremini.**
- **Aktsepteerimistingimused:**
  - ✓ Ctrl+N loob uue ülesande
  - ✓ Delete kustutab valitud ülesande
  - ✓ Ctrl+F avab otsingu
  - ✓ Otseteed on dokumenteeritud
- **Ajahinnang:** 20-35 min

## 🧪 Testimise Strateegia

### Unit Testid
```java
// Näide: TaskService test
@Test
public void testCreateTask() {
    Task task = taskService.createTask("New Task", columnId);
    assertNotNull(task.getId());
    assertEquals("New Task", task.getTitle());
}
```

### Integratsioonitestid
- Andmebaasi operatsioonid
- Drag & Drop funktsionaalsus
- Service layer integratsioon

## 🚀 Arenduse Sammud

### Sprint 1 (Põhifunktsionaalsus)
1. Projekti struktuur ja andmebaas
2. Tahvli loomine (#1)
3. Ülesande lisamine (#2)
4. Ülesande liigutamine (#3)

### Sprint 2 (Laiendatud funktsionaalsus)
5. Veeru lisamine (#4)
6. Ülesande muutmine (#5)
7. Ülesande kustutamine (#6)
8. Prioriteedi määramine (#7)

### Sprint 3 (Täiustused)
9. Tahvlite nimekiri (#8)
10. Siltide lisamine (#10)
11. Tähtaja määramine (#11)
12. Otsing (#12)

### Sprint 4 (Viimistlus)
13. Filtreerimine (#13)
14. Statistika (#18)
15. Automaatne salvestamine (#19)
16. Klaviatuuri otseteed (#20)

## 📋 Git Workflow

### Commit Message Format
```
[STORY-#] Lühike kirjeldus

- Detailne muudatus 1
- Detailne muudatus 2
```

### Branch Strategy
- `main` - Stabiilne kood
- `feature/story-#` - Üks haru kasutajaloo kohta
- Pull Request enne main'i liitmist

## 🔧 Konfiguratsiooni Näide

### pom.xml (Maven)
```xml
<dependencies>
    <dependency>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-controls</artifactId>
        <version>19</version>
    </dependency>
    <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>3.42.0.0</version>
    </dependency>
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.9.3</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

## 📚 DRY Koodinäited

### Service Layer Näide
```java
public class TaskService {
    private final TaskDAO taskDAO;
    
    public TaskService(TaskDAO taskDAO) {
        this.taskDAO = taskDAO;
    }
    
    public Task createTask(String title, int columnId) {
        validateTitle(title);
        Task task = new Task(title, columnId);
        return taskDAO.save(task);
    }
    
    public void moveTask(int taskId, int targetColumnId) {
        Task task = taskDAO.findById(taskId);
        task.setColumnId(targetColumnId);
        taskDAO.update(task);
        notifyObservers(task);
    }
    
    private void validateTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            throw new ValidationException("Title cannot be empty");
        }
    }
}
```

### Utility Class Näide
```java
public class DragDropHandler {
    public static void enableDragDrop(Node source, Consumer<DragEvent> onDrop) {
        source.setOnDragDetected(event -> {
            Dragboard db = source.startDragAndDrop(TransferMode.MOVE);
            ClipboardContent content = new ClipboardContent();
            content.putString(source.getId());
            db.setContent(content);
            event.consume();
        });
        
        source.setOnDragDropped(event -> {
            onDrop.accept(event);
            event.setDropCompleted(true);
            event.consume();
        });
    }
}
```

## 🎯 Kvaliteedi Kriteeriumid

1. **Kood on DRY** - Ei ole dubleerimist
2. **Testide katvus** - Minimaalselt 70%
3. **JavaDoc** - Kõik avalikud meetodid dokumenteeritud
4. **Clean Code** - Järgib Java konventsioone
5. **SOLID printsiibid** - Eriti Single Responsibility

## 📞 Stand-up Mall

```
VALMIS:
- [Lõpetatud kasutajalugu/ülesanne]
- [Testid ja dokumentatsioon]

TÖÖS:
- [Praegune kasutajalugu #X]
- [Progress protsentides]

TAKISTUSED:
- [Tehniline probleem või küsimus]
- [Vajadus abi järele]
```

---

**Edu projektiga! 🚀**