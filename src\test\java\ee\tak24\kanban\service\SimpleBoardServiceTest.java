package ee.tak24.kanban.service;

import ee.tak24.kanban.model.Board;

import java.sql.SQLException;
import java.util.List;

/**
 * Simple test for Story #1: Tahvli loomine
 * 
 * Aktsepteerimistingimused:
 * ✓ <PERSON><PERSON><PERSON><PERSON> saab sisestada tahvli nime
 * ✓ Tahvel luuakse vaikimisi 3 veeruga (TODO, IN PROGRESS, DONE)
 * ✓ Tahvel salvestatakse andmebaasi
 * ✓ Uus tahvel avaneb kohe peale loomist
 */
public class SimpleBoardServiceTest {

    private BoardService boardService;

    public void setUp() {
        boardService = new BoardService();
    }

    public void testCreateBoard_Success() throws SQLException {
        System.out.println("Testing board creation...");
        
        // Given
        String boardName = "Test Project Board";

        // When
        Board createdBoard = boardService.createBoard(boardName);

        // Then
        assert createdBoard != null : "Board should be created";
        assert createdBoard.getId() != null : "Board should have an ID";
        assert boardName.equals(createdBoard.getName()) : "Board name should match input";
        assert createdBoard.getCreatedAt() != null : "Board should have creation timestamp";

        // Verify board can be retrieved from database
        Board retrievedBoard = boardService.getBoardById(createdBoard.getId());
        assert retrievedBoard != null : "Board should be retrievable from database";
        assert boardName.equals(retrievedBoard.getName()) : "Retrieved board name should match";
        
        System.out.println("✓ Board created successfully with ID: " + createdBoard.getId());
    }

    public void testCreateBoard_WithDefaultColumns() throws SQLException {
        System.out.println("Testing board creation with default columns...");
        
        // Given
        String boardName = "Project with Default Columns";

        // When
        Board createdBoard = boardService.createBoard(boardName);

        // Then
        assert createdBoard != null : "Board should be created";
        assert createdBoard.getId() != null : "Board should have an ID";
        assert createdBoard.getId() > 0 : "Board ID should be positive";
        
        System.out.println("✓ Board with default columns created successfully");
    }

    public void testCreateBoard_EmptyName_ThrowsException() {
        System.out.println("Testing board creation with empty name...");
        
        // Given
        String emptyName = "";

        // When & Then
        try {
            boardService.createBoard(emptyName);
            assert false : "Should throw exception for empty board name";
        } catch (IllegalArgumentException e) {
            assert "Board name cannot be empty".equals(e.getMessage()) : "Wrong exception message";
            System.out.println("✓ Empty name validation works correctly");
        } catch (Exception e) {
            assert false : "Wrong exception type: " + e.getClass().getSimpleName();
        }
    }

    public void testCreateBoard_NullName_ThrowsException() {
        System.out.println("Testing board creation with null name...");
        
        // Given
        String nullName = null;

        // When & Then
        try {
            boardService.createBoard(nullName);
            assert false : "Should throw exception for null board name";
        } catch (IllegalArgumentException e) {
            assert "Board name cannot be empty".equals(e.getMessage()) : "Wrong exception message";
            System.out.println("✓ Null name validation works correctly");
        } catch (Exception e) {
            assert false : "Wrong exception type: " + e.getClass().getSimpleName();
        }
    }

    public void testGetAllBoards_IncludesNewBoard() throws SQLException {
        System.out.println("Testing board list functionality...");
        
        // Given
        String boardName = "Test Board for List";
        Board createdBoard = boardService.createBoard(boardName);

        // When
        List<Board> allBoards = boardService.getAllBoards();

        // Then
        assert allBoards != null : "Board list should not be null";
        assert allBoards.size() > 0 : "Board list should contain at least one board";
        
        boolean foundBoard = allBoards.stream()
            .anyMatch(board -> board.getId().equals(createdBoard.getId()));
        assert foundBoard : "Created board should be in the list";
        
        System.out.println("✓ Board list contains " + allBoards.size() + " boards");
    }

    public static void main(String[] args) {
        System.out.println("=== Story #1: Tahvli loomine - Test Suite ===");
        
        SimpleBoardServiceTest test = new SimpleBoardServiceTest();
        int passed = 0;
        int total = 0;
        
        try {
            test.setUp();
            test.testCreateBoard_Success();
            passed++;
        } catch (Exception e) {
            System.out.println("✗ testCreateBoard_Success FAILED: " + e.getMessage());
            e.printStackTrace();
        }
        total++;
        
        try {
            test.setUp();
            test.testCreateBoard_WithDefaultColumns();
            passed++;
        } catch (Exception e) {
            System.out.println("✗ testCreateBoard_WithDefaultColumns FAILED: " + e.getMessage());
        }
        total++;
        
        try {
            test.setUp();
            test.testCreateBoard_EmptyName_ThrowsException();
            passed++;
        } catch (Exception e) {
            System.out.println("✗ testCreateBoard_EmptyName_ThrowsException FAILED: " + e.getMessage());
        }
        total++;
        
        try {
            test.setUp();
            test.testCreateBoard_NullName_ThrowsException();
            passed++;
        } catch (Exception e) {
            System.out.println("✗ testCreateBoard_NullName_ThrowsException FAILED: " + e.getMessage());
        }
        total++;
        
        try {
            test.setUp();
            test.testGetAllBoards_IncludesNewBoard();
            passed++;
        } catch (Exception e) {
            System.out.println("✗ testGetAllBoards_IncludesNewBoard FAILED: " + e.getMessage());
        }
        total++;
        
        System.out.println("\n=== Test Results ===");
        System.out.println("Passed: " + passed + "/" + total);
        
        if (passed == total) {
            System.out.println("🎉 ALL TESTS PASSED! Story #1 is ready for commit.");
        } else {
            System.out.println("❌ Some tests failed. Fix issues before commit.");
        }
    }
}
