package ee.tak24.kanban.service;

import ee.tak24.kanban.database.BoardDAO;
import ee.tak24.kanban.model.Board;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.sql.SQLException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for Story #1: Tahvli loomine
 * 
 * Aktsepteerimistingimused:
 * ✓ Kasutaja saab sisestada tahvli nime
 * ✓ Tahvel luuakse vaikimisi 3 veeruga (TODO, IN PROGRESS, DONE)
 * ✓ Tahvel salvestatakse andmebaasi
 * ✓ Uus tahvel avaneb kohe peale loomist
 */
public class BoardServiceTest {

    private BoardService boardService;

    @BeforeEach
    public void setUp() {
        boardService = new BoardService();
    }

    @Test
    public void testCreateBoard_Success() throws SQLException {
        // Given
        String boardName = "Test Project Board";

        // When
        Board createdBoard = boardService.createBoard(boardName);

        // Then
        assertNotNull(createdBoard, "Board should be created");
        assertNotNull(createdBoard.getId(), "Board should have an ID");
        assertEquals(boardName, createdBoard.getName(), "Board name should match input");
        assertNotNull(createdBoard.getCreatedAt(), "Board should have creation timestamp");

        // Verify board can be retrieved from database
        Board retrievedBoard = boardService.getBoardById(createdBoard.getId());
        assertNotNull(retrievedBoard, "Board should be retrievable from database");
        assertEquals(boardName, retrievedBoard.getName(), "Retrieved board name should match");
    }

    @Test
    public void testCreateBoard_WithDefaultColumns() throws SQLException {
        // Given
        String boardName = "Project with Default Columns";

        // When
        Board createdBoard = boardService.createBoard(boardName);

        // Then
        assertNotNull(createdBoard, "Board should be created");
        assertNotNull(createdBoard.getId(), "Board should have an ID");

        // Verify default columns are created (this will be tested more thoroughly in ColumnService tests)
        // For now, we just verify the board creation was successful
        assertTrue(createdBoard.getId() > 0, "Board ID should be positive");
    }

    @Test
    public void testCreateBoard_EmptyName_ThrowsException() {
        // Given
        String emptyName = "";

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> boardService.createBoard(emptyName),
            "Should throw exception for empty board name"
        );
        
        assertEquals("Board name cannot be empty", exception.getMessage());
    }

    @Test
    void testCreateBoard_NullName_ThrowsException() {
        // Given
        String nullName = null;

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> boardService.createBoard(nullName),
            "Should throw exception for null board name"
        );
        
        assertEquals("Board name cannot be empty", exception.getMessage());
    }

    @Test
    void testCreateBoard_TooLongName_ThrowsException() {
        // Given
        String tooLongName = "A".repeat(101); // 101 characters

        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> boardService.createBoard(tooLongName),
            "Should throw exception for too long board name"
        );
        
        assertEquals("Board name cannot be longer than 100 characters", exception.getMessage());
    }

    @Test
    void testGetAllBoards_IncludesNewBoard() throws SQLException {
        // Given
        String boardName = "Test Board for List";
        Board createdBoard = boardService.createBoard(boardName);

        // When
        List<Board> allBoards = boardService.getAllBoards();

        // Then
        assertNotNull(allBoards, "Board list should not be null");
        assertTrue(allBoards.size() > 0, "Board list should contain at least one board");
        
        boolean foundBoard = allBoards.stream()
            .anyMatch(board -> board.getId().equals(createdBoard.getId()));
        assertTrue(foundBoard, "Created board should be in the list");
    }
}
