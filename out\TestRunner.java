package ee.tak24.kanban.test; 
 
import ee.tak24.kanban.service.BoardServiceTest; 
 
public class TestRunner { 
    public static void main(String[] args) { 
        System.out.println("Running BoardServiceTest..."); 
        BoardServiceTest test = new BoardServiceTest(); 
        try { 
            test.setUp(); 
            test.testCreateBoard_Success(); 
            System.out.println("✓ testCreateBoard_Success PASSED"); 
        } catch (Exception e) { 
            System.out.println("✗ testCreateBoard_Success FAILED: " + e.getMessage()); 
            e.printStackTrace(); 
        } 
        try { 
            test.setUp(); 
            test.testCreateBoard_WithDefaultColumns(); 
            System.out.println("✓ testCreateBoard_WithDefaultColumns PASSED"); 
        } catch (Exception e) { 
            System.out.println("✗ testCreateBoard_WithDefaultColumns FAILED: " + e.getMessage()); 
        } 
        try { 
            test.setUp(); 
            test.testCreateBoard_EmptyName_ThrowsException(); 
            System.out.println("✓ testCreateBoard_EmptyName_ThrowsException PASSED"); 
        } catch (Exception e) { 
            System.out.println("✗ testCreateBoard_EmptyName_ThrowsException FAILED: " + e.getMessage()); 
        } 
        System.out.println("Test run completed."); 
    } 
} 
