@echo off
echo Running BoardServiceTest manually...

REM Compile if needed
if not exist "out\classes" call compile-and-test.bat

REM Create a simple test runner
echo Creating test runner...

echo package ee.tak24.kanban.test; > out\TestRunner.java
echo. >> out\TestRunner.java
echo import ee.tak24.kanban.service.BoardServiceTest; >> out\TestRunner.java
echo. >> out\TestRunner.java
echo public class TestRunner { >> out\TestRunner.java
echo     public static void main(String[] args) { >> out\TestRunner.java
echo         System.out.println("Running BoardServiceTest..."); >> out\TestRunner.java
echo         BoardServiceTest test = new BoardServiceTest(); >> out\TestRunner.java
echo         try { >> out\TestRunner.java
echo             test.setUp(); >> out\TestRunner.java
echo             test.testCreateBoard_Success(); >> out\TestRunner.java
echo             System.out.println("✓ testCreateBoard_Success PASSED"); >> out\TestRunner.java
echo         } catch (Exception e) { >> out\TestRunner.java
echo             System.out.println("✗ testCreateBoard_Success FAILED: " + e.getMessage()); >> out\TestRunner.java
echo             e.printStackTrace(); >> out\TestRunner.java
echo         } >> out\TestRunner.java
echo         try { >> out\TestRunner.java
echo             test.setUp(); >> out\TestRunner.java
echo             test.testCreateBoard_WithDefaultColumns(); >> out\TestRunner.java
echo             System.out.println("✓ testCreateBoard_WithDefaultColumns PASSED"); >> out\TestRunner.java
echo         } catch (Exception e) { >> out\TestRunner.java
echo             System.out.println("✗ testCreateBoard_WithDefaultColumns FAILED: " + e.getMessage()); >> out\TestRunner.java
echo         } >> out\TestRunner.java
echo         try { >> out\TestRunner.java
echo             test.setUp(); >> out\TestRunner.java
echo             test.testCreateBoard_EmptyName_ThrowsException(); >> out\TestRunner.java
echo             System.out.println("✓ testCreateBoard_EmptyName_ThrowsException PASSED"); >> out\TestRunner.java
echo         } catch (Exception e) { >> out\TestRunner.java
echo             System.out.println("✗ testCreateBoard_EmptyName_ThrowsException FAILED: " + e.getMessage()); >> out\TestRunner.java
echo         } >> out\TestRunner.java
echo         System.out.println("Test run completed."); >> out\TestRunner.java
echo     } >> out\TestRunner.java
echo } >> out\TestRunner.java

REM Compile test runner
javac -cp "out\classes;out\test-classes;lib\sqlite-jdbc-3.42.0.0.jar" -d out out\TestRunner.java

REM Run test
java -cp "out;out\classes;out\test-classes;lib\sqlite-jdbc-3.42.0.0.jar" ee.tak24.kanban.test.TestRunner
